// SSAC后端登录响应格式
export interface LoginResult {
  token: string  // 直接返回token字符串
}

// 用户信息响应格式 - 对应SSAC后端getInfo接口
export interface UserInfoResult {
  user: {
    userId: number
    userName: string
    nickName: string
    email?: string
    phonenumber?: string
    sex?: string
    avatar?: string
    deptId?: number
    dept?: {
      deptId: number
      deptName: string
      deptUserId: number
      deptUserName: string
    }
  }
  roles: string[]
  permissions: string[]
  mqtt: boolean
  language: string
}

// 验证码响应格式
export interface CaptchaResult {
  captchaEnabled: boolean
  uuid?: string
  img?: string
  resultCode?: string
}
