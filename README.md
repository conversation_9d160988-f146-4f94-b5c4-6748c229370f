## 总览
fastbee-view是基于GoView二次开发的低代码可视化平台，图表或页面元素封装为基础组件，无需编写代码即可完成业务需求，项目已经获得GoView的商用授权。

技术栈为：Vue3 + TypeScript4 + Vite2 + NaiveUI + ECharts5 + Axios + Pinia2 + PlopJS

使用流程：新建项目-->可以点击上方编辑名称-->尽情发挥拖拉拽编辑页面-->保存(60s自动保存/手动保存)-->预览-->发布（生成地址）

注： 建议使用pnpm下载依赖和打包，npm有时候会有部分依赖下载不成功到时打包失败。


## 项目运行和部署
#### 1.修改根目录的 `.env.development` 文件，配置后端接口地址
```
# 后端接口地址
VITE_DEV_PATH = 'http://localhost:8080'
```

#### 2.修改完成后，开始安装依赖，建议使用 pnpm，比较快

```shell
# 安装 pnpm 
npm install -g pnpm
```

```shell
# 安装项目依赖

# pnpm（建议使用nrm切换到淘宝源 https://registry.npmmirror.com/）
pnpm install

# npm
npm install

# yarn
yarn install
```

#### 3.启动项目
```shell
#pnpm
pnpm dev

# npm
npm run dev

#yarn
yarn dev

#Makefile
make dev
```

#### 4.编译
```shell
#pnpm
pnpm run build

# npm
npm run build

#yarn
yarn run build

#Makefile
make dist
```

#### 5.部署：
Nginx配置文件中添加location ，切记不要用重复的匹配关键词，编译的文件上传到 `/var/data/nginx/view/` 目录下
```shell
  location ^~/view/ {
    alias /var/data/nginx/view/;
    index  index.html;
    try_files $uri $uri/ /index.html last;
  }
```

部署完访问地址： https://[域名]/view/ ，也可以通过在系统中添加外链菜单
```
# 参考外链地址：
https://iot.fastbee.cn/view/#/project/items
```

## 相关文档 
* [GoView官网文档](https://www.mtruning.club/guide/start/)
* [FastBee仓库](https://code.wumei.live/)

## 二次开发说明
* 登陆、退出、获取用户信息适配物美后台统一接口，拦截器调整
* 修改上传文件功能：原项目是上传到minio服务(oss),现在直接融合到wumei-smart项目中，一个大屏固定一张预览图
* 大屏业务数据字段微调，对应后台代码基础代码微调
* 所有的接口地址位置：`src\api\path\*`
  * 系统登陆相关 `system.api` ===> 对应物美系统的接口
  * 项目相关：`project.api` ===> `com.ruoyi.iot.controller.GoviewProjectController`
* 项目使用接口列表如下：
```ts
项目列表 GET  /goview/project/list
新增项目 POST  /goview/project
保存项目内容 POST /goview/project/save/data
修改项目基础信息/修改发布状态  PUT  /goview/project/
删除项目 DELETE /goview/project/{ids} 
上传文件接口  /goview/project/upload
获取项目 GET /goview/project/getData 此接口涉及到预览，所以前后端白名单放行
```

## 更新记录
* 2022-12-22 更新到go-view基础版本2.1.3
* 2023-05-30 更新到go-view基础版本2.2.3 [PS:版本依赖更新较大，建议删除依赖并且重新intsall项目]
* 2023-12-28 更新到go-view基础版本2.2.7 [PS:有新依赖，建议删除依赖并且重新intsall项目]
