// 项目列表项 - 对应SSAC后端GoviewProject实体
export type ProjectItem = {
  /**
   * 项目 id
   */
  id: string
  /**
   * 项目名称
   */
  projectName: string
  /**
   * 项目状态:
   * 0: 未发布
   * 1: 已发布
   */
  state: number
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 更新时间
   */
  updateTime?: string
  /**
   * 预览图片url
   */
  indexImage: string
  /**
   * 创建者
   */
  createBy: string
  /**
   * 创建者 id (兼容旧版本)
   */
  createUserId?: string
  /**
   * 项目备注
   */
  remarks?: string
  /**
   * 租户ID
   */
  tenantId?: number
  /**
   * 租户名称
   */
  tenantName?: string
}

// 项目详情 - 包含项目内容数据
export interface ProjectDetail extends ProjectItem {
  /**
   * 项目参数/内容
   */
   content?: string
}

// 项目数据保存请求
export interface ProjectDataSaveRequest {
  projectId: string
  content: string
}

// 文件上传响应
export interface UploadResponse {
  fileName: string
  url: string
}